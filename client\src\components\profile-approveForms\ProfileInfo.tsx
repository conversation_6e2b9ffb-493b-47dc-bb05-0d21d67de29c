import { useForm } from "@mantine/form";
import {
	TextInput,
	Button,
	Group,
	Stack,
	Text,
	Flex,
	Select,
	ThemeIcon,
	Card,
} from "@mantine/core";
import { useState } from "react";
import type { BasicDetailsDataType } from "../../types";
import { allowedRoles, roleLabels, roleValues } from "../../constants";
import { isAxiosError } from "axios";
import {
	IconX,
	IconUser,
	IconMail,
	IconPhone,
	IconBriefcase,
	IconCheck,
} from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import apiClient from "../../config/axios";
import { rolesLabelMap } from "../../constants";

type basicDataTypeProps = BasicDetailsDataType & {
	userId?: string;
	fetchProfile: () => void;
};

const ProfileInfo = ({
	firstName,
	secondName,
	email,
	mobile,
	role,
	userId,
	fetchProfile,
}: basicDataTypeProps) => {
	const [editing, setEditing] = useState(false);

	const form = useForm({
		initialValues: {
			firstName: firstName,
			lastName: secondName,
			email: email,
			mobile: mobile,
			role: roleLabels[role as keyof typeof roleLabels],
		},
		validate: {
			firstName: val => (!val ? "First name is required" : null),
			lastName: val => (!val ? "Last name is required" : null),
			role: val => (!val ? "Please select a role" : null),
		},
		transformValues: values => ({
			firstName: values.firstName?.trim() || "",
			lastName: values.lastName?.trim() || "",
			email: values.email?.trim() || "",
			mobile: values.mobile?.trim() || "",
			role: values.role,
		}),
	});

	const handleCancel = () => {
		form.reset();
		setEditing(false);
	};

	const handleSubmit = async (values: typeof form.values) => {
		if (!userId) {
			notifications.show({
				title: "Error",
				message: "Failed to update profile",
				color: "red",
				icon: <IconX />,
			});
			console.log(`No user ID provided for updating profile`);
			return;
		}
		try {
			const response = await apiClient.put(
				`/api/users/basic-details/${userId}`,
				{
					...values,
					role: roleValues[values.role as keyof typeof roleValues],
				}
			);
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
				icon: <IconCheck />,
			});
			console.log("Submitted:", values);
			setEditing(false);
			fetchProfile();
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Error",
					message:
						error.response?.data?.message ||
						"Failed to update profile",
					color: "red",
					icon: <IconX />,
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to update profile",
					color: "red",
					icon: <IconX />,
				});
			}
		}
	};

	return (
		<>
			<form>
				<>
					{!editing && (
						<Flex justify="flex-end">
							<Button onClick={() => setEditing(true)} mb={8}>
								Edit
							</Button>
						</Flex>
					)}

					<Stack>
						{editing ? (
							<Stack gap="xs">
								{editing && (
									<Flex justify={"flex-end"}>
										<Button
											variant="outline"
											onClick={handleCancel}
										>
											Cancel
										</Button>
									</Flex>
								)}
								<TextInput
									label="First Name"
									placeholder="Enter first name"
									{...form.getInputProps("firstName")}
									required
								/>
								<TextInput
									label="Last Name"
									placeholder="Enter last name"
									{...form.getInputProps("lastName")}
									required
								/>
								<TextInput
									label="Email"
									{...form.getInputProps("email")}
									disabled={true}
								/>
								<TextInput
									label="Mobile"
									{...form.getInputProps("mobile")}
									disabled={true}
								/>
								<Select
									label="Role"
									placeholder="Select role"
									data={allowedRoles.map(r => ({
										value: r,
										label: rolesLabelMap[
											r as keyof typeof rolesLabelMap
										],
									}))}
									{...form.getInputProps("role")}
									required
									disabled={true}
								/>

								{editing && (
									<Flex justify={"flex-end"}>
										<Button
											onClick={() =>
												form.onSubmit(handleSubmit)()
											}
										>
											Save
										</Button>
									</Flex>
								)}
							</Stack>
						) : (
							<Card shadow="lg" withBorder bdrs={"lg"}>
								<Stack gap="lg">
									<Group gap="xs">
										<ThemeIcon variant="light" color="blue">
											<IconUser size={16} />
										</ThemeIcon>
										<Stack gap={-4}>
											<Text c="dimmed" size="xs">
												First Name:
											</Text>
											<Text size="sm">
												{form.values.firstName || "N/A"}
											</Text>
										</Stack>
									</Group>

									<Group gap="xs">
										<ThemeIcon
											variant="light"
											color="green"
										>
											<IconUser size={16} />
										</ThemeIcon>
										<Stack gap={-4}>
											<Text c="dimmed" size="xs">
												Last Name:
											</Text>
											<Text size="sm">
												{form.values.lastName || "N/A"}
											</Text>
										</Stack>
									</Group>

									<Group gap="xs">
										<ThemeIcon variant="light" color="teal">
											<IconMail size={16} />
										</ThemeIcon>
										<Stack gap={-4}>
											<Text c="dimmed" size="xs">
												Email:
											</Text>
											<Text size="sm">
												{form.values.email || "N/A"}
											</Text>
										</Stack>
									</Group>

									<Group gap="xs">
										<ThemeIcon
											variant="light"
											color="violet"
										>
											<IconPhone size={16} />
										</ThemeIcon>
										<Stack gap={-4}>
											<Text c="dimmed" size="xs">
												Mobile:
											</Text>
											<Text size="sm">
												{form.values.mobile || "N/A"}
											</Text>
										</Stack>
									</Group>

									<Group gap="xs">
										<ThemeIcon
											variant="light"
											color="yellow"
										>
											<IconBriefcase size={16} />
										</ThemeIcon>
										<Stack gap={-4}>
											<Text c="dimmed" size="xs">
												Role:
											</Text>
											<Text size="sm">
												{rolesLabelMap[
													form.values.role
												] || "N/A"}
											</Text>
										</Stack>
									</Group>
								</Stack>
							</Card>
						)}
					</Stack>
				</>
			</form>
		</>
	);
};

export default ProfileInfo;
