import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import FullScreenLoader from "./FullScreenLoader";
import { useEffect } from "react";

const SwitchingPanel = () => {
	const { user, isUpdatingUser, isSwitchingAdminPanel } = useAuth();
	const navigate = useNavigate();

	useEffect(() => {
		// Only navigate if we have a user and we're not currently updating or switching
		if (user && !isUpdatingUser && !isSwitchingAdminPanel) {
			console.log(user.isAdminPanelUser);
			// Add a small delay to ensure smooth transition
			const timeoutId = setTimeout(() => {
				if (user.isAdminPanelUser) {
					navigate("/profile");
				} else {
					navigate("/");
				}
			}, 100);

			return () => clearTimeout(timeoutId);
		}
	}, [user, navigate, isUpdatingUser, isSwitchingAdminPanel]);
	return <FullScreenLoader />;
};

export default SwitchingPanel;
