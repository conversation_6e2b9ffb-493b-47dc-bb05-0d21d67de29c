import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import FullScreenLoader from "./FullScreenLoader";
import { useEffect } from "react";

const SwitchingPanel = () => {
	const { user, isUpdatingUser } = useAuth();
	const navigate = useNavigate();

	useEffect(() => {
		// Only navigate if we have a user and we're not currently updating
		if (user && !isUpdatingUser) {
			console.log(user.isAdminPanelUser);
			// Add a small delay to ensure smooth transition
			const timeoutId = setTimeout(() => {
				if (user.isAdminPanelUser) {
					navigate("/profile");
				} else {
					navigate("/");
				}
			}, 100);

			return () => clearTimeout(timeoutId);
		}
	}, [user, navigate, isUpdatingUser]);
	return <FullScreenLoader />;
};

export default SwitchingPanel;
