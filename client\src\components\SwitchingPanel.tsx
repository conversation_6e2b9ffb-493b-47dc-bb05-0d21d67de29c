import { useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import FullScreenLoader from "./FullScreenLoader";
import { useEffect } from "react";

const SwitchingPanel = () => {
	const { user } = useAuth();
	const navigate = useNavigate();

	useEffect(() => {
		if (user) {
			console.log(user.isAdminPanelUser);
			if (user.isAdminPanelUser) {
				navigate("/profile");
			} else {
				navigate("/");
			}
		}
	}, [user, navigate]);
	return <FullScreenLoader />;
};

export default SwitchingPanel;
