import {
	AppShell,
	Divider,
	NavLink,
	Stack,
	TextInput,
	Title,
} from "@mantine/core";
import {
	Routes,
	Route,
	useLocation,
	useNavigate,
	matchPath,
	Navigate,
} from "react-router-dom";
import PrivateRoute from "../components/PrivateRoute";
import Login from "../pages/Login";
import { useDisclosure } from "@mantine/hooks";
import {
	IconUser,
	IconView360Number,
	IconUserCheck,
	IconSearch,
	type IconProps,
	type Icon,
} from "@tabler/icons-react";
import { useAuth } from "../contexts/AuthContext";
import Users from "../pages/Users";
import ForgotPassword from "../pages/ForgotPassword";
import ResetPassword from "../pages/ResetPassword";
import NotFound from "../pages/NotFound";
import ProfileTabs from "../components/ProfileTabs";
import { ONBOARDING_STEP, roleValues } from "../constants";
import OnBoardingStepper from "../pages/OnBoardingStepper";
import WaitForApproval from "../components/WaitForApproval";
import ProfileReview from "../pages/ProfileReview";
import Header from "../components/Header";
import PendingApprovals from "../pages/PendingApprovals";
import { openUserSearchModal } from "../components/modals/UserSearchModal";
import SearchedUser from "../pages/SearchedUser";
import { useMemo } from "react";
import SwitchingPanel from "../components/SwitchingPanel";

type NavItemsDataType =
	| {
			type: "navigation";
			label: string;
			icon: React.ForwardRefExoticComponent<
				IconProps & React.RefAttributes<Icon>
			>;
			path: string;
			showIf: boolean;
	  }
	| {
			type: "divider";
			label: string;
			showIf: boolean;
	  };

function AppLayout() {
	const { user } = useAuth();
	const location = useLocation();
	const [opened, { toggle }] = useDisclosure();
	const navigate = useNavigate();

	const navItems: NavItemsDataType[] = useMemo(() => {
		if (!user) return [];
		const isNotCommunityMember = user.role !== roleValues.CommunityMember;
		const items = [
			{
				type: "navigation" as const,
				label: "Profile",
				icon: IconView360Number,
				path: "/profile",
				showIf: true,
			},
			{
				type: "divider" as const,
				label: "Admin Controls",
				showIf: isNotCommunityMember,
			},
			{
				type: "navigation" as const,
				label: "Users",
				icon: IconUser,
				path: "/users",
				showIf: isNotCommunityMember,
			},
			{
				type: "navigation" as const,
				label: "Pending Profiles",
				icon: IconUserCheck,
				path: "/pending-profiles",
				showIf: isNotCommunityMember,
			},
		];
		return items;
	}, [user]);

	const isOnboardingStep = useMemo(() => {
		if (!user) return false;
		if (
			user.onboardingStep < ONBOARDING_STEP.WAIT_FOR_APPROVAL &&
			!user.isAdminPanelUser
		) {
			return true;
		}
		return false;
	}, [user]);

	const isWaitForApproval = useMemo(() => {
		if (!user) return false;
		if (
			user.onboardingStep === ONBOARDING_STEP.WAIT_FOR_APPROVAL &&
			!user.isAdminPanelUser
		) {
			return true;
		}
		return false;
	}, [user]);

	const isPublicRoute =
		location.pathname === "/login" ||
		location.pathname === "/forgot-password" ||
		matchPath("/reset-password/:token", location.pathname);

	if (!user) {
		return (
			<Routes>
				<Route path="/login" element={<Login />} />
				<Route path="/forgot-password" element={<ForgotPassword />} />
				<Route
					path="/reset-password/:token"
					element={<ResetPassword />}
				/>
				<Route path="*" element={<Navigate to="/login" replace />} />
			</Routes>
		);
	}

	if (isPublicRoute) {
		if (user.onboardingStep <= ONBOARDING_STEP.WAIT_FOR_APPROVAL) {
			return <Navigate to="/" replace />;
		}
		const redirectTo =
			user.role === roleValues.CommunityMember ? "/profile" : "/users";
		return <Navigate to={redirectTo} replace />;
	}

	if (isOnboardingStep) {
		return (
			<AppShell
				layout="alt"
				header={{ height: 70 }}
				styles={{
					root: { overflow: "hidden" },
				}}
			>
				<Header />

				<AppShell.Main>
					<Routes>
						<Route path="/:step?" element={<OnBoardingStepper />} />
						<Route path="/switching" element={<SwitchingPanel />} />
						<Route
							path="*"
							element={<Navigate to="/:step?" replace />}
						/>
					</Routes>
				</AppShell.Main>
			</AppShell>
		);
	}

	if (isWaitForApproval) {
		return (
			<AppShell layout="alt" header={{ height: 70 }}>
				<Header />

				<AppShell.Main>
					<Routes>
						<Route path="/" element={<WaitForApproval />} />
						<Route path="/switching" element={<SwitchingPanel />} />
						<Route path="*" element={<Navigate to="/" replace />} />
					</Routes>
				</AppShell.Main>
			</AppShell>
		);
	}

	return (
		<AppShell
			layout="alt"
			padding="md"
			header={{ height: 70 }}
			navbar={{
				width: 300,
				breakpoint: "sm",
				collapsed: { mobile: !opened },
			}}
		>
			<Header opened={opened} toggle={toggle} showTitle={false} />

			<AppShell.Navbar h="100%" p="md">
				<Stack>
					<Title order={1} fw={600}>
						360 APP
					</Title>

					<TextInput
						leftSection={<IconSearch size={16} />}
						placeholder="Search Users..."
						pointer
						onClick={e => {
							e.currentTarget.blur();
							openUserSearchModal();
						}}
					/>

					{navItems.map((item, index) => {
						if (item.type === "divider") {
							return (
								<Divider
									key={index}
									label={item.label}
									hidden={!item.showIf ? true : false}
								/>
							);
						}
						return (
							<NavLink
								key={index}
								label={item.label}
								leftSection={<item.icon size={16} />}
								onClick={() => navigate(item.path)}
								active={location.pathname === item.path}
								hidden={!item.showIf ? true : false}
							/>
						);
					})}
				</Stack>
			</AppShell.Navbar>

			<AppShell.Main>
				<Routes>
					<Route
						path="/"
						element={
							<PrivateRoute>
								{user?.role === roleValues.CommunityMember ? (
									<Navigate to="/profile" replace />
								) : (
									<Navigate to="/users" replace />
								)}
							</PrivateRoute>
						}
					/>
					{user?.role !== roleValues.CommunityMember && (
						<>
							<Route
								path="/users"
								element={
									<PrivateRoute>
										<Users />
									</PrivateRoute>
								}
							/>
						</>
					)}
					<Route
						path="/profile"
						element={
							<PrivateRoute>
								<ProfileTabs />
							</PrivateRoute>
						}
					/>
					{user?.canApproveUser && (
						<Route path="/pending-profiles">
							<Route
								index
								element={
									<PrivateRoute>
										<PendingApprovals />
									</PrivateRoute>
								}
							/>
							<Route
								path="user/:userId"
								element={
									<PrivateRoute>
										<ProfileReview />
									</PrivateRoute>
								}
							></Route>
						</Route>
					)}
					<Route
						path="search/:userId"
						element={
							<PrivateRoute>
								<SearchedUser />
							</PrivateRoute>
						}
					/>
					<Route path="/switching" element={<SwitchingPanel />} />
					<Route path="*" element={<NotFound />} />
				</Routes>
			</AppShell.Main>
		</AppShell>
	);
}

export default AppLayout;
