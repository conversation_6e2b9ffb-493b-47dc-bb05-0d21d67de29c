import {
	<PERSON>ack,
	Group,
	Text,
	Divider,
	ThemeIcon,
	Flex,
	Box,
	Button,
} from "@mantine/core";
import {
	IconBriefcase,
	IconUserCheck,
	IconBuilding,
	IconTags,
} from "@tabler/icons-react";
import React, { useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { ProfessionalLifeDataType } from "../../types";
import VideoPreviewAndUpload from "../VideoPreviewAndUpload";
import FullScreenLoader from "../FullScreenLoader";

interface ProfessionalLifePreviewProps {
	showEdit?: boolean;
	setEditing?: (value: boolean) => void;
	lifeData?: ProfessionalLifeDataType;
}

const ProfessionalLifePreview: React.FC<ProfessionalLifePreviewProps> = ({
	showEdit,
	setEditing,
	lifeData,
}) => {
	const [professionalLife, setProfessionalLife] =
		useState<ProfessionalLifeDataType | null>(null);

	const fetchData = async () => {
		try {
			const response = await apiClient.get<ProfessionalLifeDataType>(
				"/api/lifeData/professionalLife"
			);
			setProfessionalLife(response.data);
		} catch (err) {
			console.error(`Error fetching professional life data: ${err}`);
		}
	};

	useEffect(() => {
		if (lifeData) {
			setProfessionalLife(lifeData);
		} else {
			fetchData();
		}
	}, [lifeData]);

	if (!professionalLife) return <FullScreenLoader />;

	return (
		<>
			{showEdit && (
				<Flex justify="flex-end">
					<Button onClick={() => setEditing?.(true)} mb={8}>
						Edit
					</Button>
				</Flex>
			)}
			<VideoPreviewAndUpload
				editing={true}
				videoPreviewUrl={professionalLife.videoUrl}
				videoType="ProfessionalLife"
			/>

			<Stack gap="lg">
				<Group align="center" gap="xs">
					<ThemeIcon variant="light" color="blue">
						<IconBriefcase size={20} />
					</ThemeIcon>
					<Text className="font-semibold">First Job - Company:</Text>
					<Text>
						{professionalLife.firstJob.companyName || "N/A"}
					</Text>
				</Group>

				<Stack gap="xs">
					{professionalLife.firstJob.roles &&
					professionalLife.firstJob.roles.length > 0 ? (
						professionalLife.firstJob.roles.map((role, idx) => (
							<Group key={idx} gap="xs">
								<ThemeIcon variant="light" color="teal">
									<IconUserCheck size={16} />
								</ThemeIcon>
								<Stack gap={-4}>
									<Text c="dimmed" size="xs">
										Role {idx + 1}
									</Text>
									<Text size="sm">{role || "N/A"}</Text>
								</Stack>
							</Group>
						))
					) : (
						<Text size="sm" c="dimmed">
							No First Job Roles to display.
						</Text>
					)}
				</Stack>

				<Divider label="Subsequent Jobs" labelPosition="center" />

				{professionalLife.subsequentJobs &&
				professionalLife.subsequentJobs.length > 0 ? (
					professionalLife.subsequentJobs.map((job, i) => (
						<Stack key={i} gap="xs">
							<Group align="center" gap="xs">
								<ThemeIcon variant="light" color="violet">
									<IconBuilding size={20} />
								</ThemeIcon>
								<Text className="font-semibold">
									Company {i + 1}:
								</Text>
								<Text>{job.companyName || "N/A"}</Text>
							</Group>

							{job.roles.map((role, j) => (
								<Group key={j} gap="xs">
									<ThemeIcon variant="light" color="teal">
										<IconUserCheck size={16} />
									</ThemeIcon>
									<Stack gap={-4}>
										<Text c="dimmed" size="xs">
											Role {j + 1}
										</Text>
										<Text size="sm">{role || "N/A"}</Text>
									</Stack>
								</Group>
							))}

							{i < professionalLife.subsequentJobs.length - 1 && (
								<Divider className="my-2 border-gray-200" />
							)}
						</Stack>
					))
				) : (
					<Text size="sm" c="dimmed">
						No Subsequent Jobs to display.
					</Text>
				)}

				<Divider label="Tags" labelPosition="center" />

				<Flex align="start" gap="sm">
					<ThemeIcon variant="light" color="yellow" mt={2}>
						<IconTags size={16} />
					</ThemeIcon>

					<Box style={{ flex: 1 }}>
						{professionalLife.professionalLifeTags.length > 0 ? (
							<Flex wrap="wrap" gap="xs">
								{professionalLife.professionalLifeTags.map(
									(tag, idx) => (
										<Text
											key={idx}
											size="sm"
											className="bg-gray-100 text-sm !px-3 !py-1 rounded-full text-gray-700"
										>
											{tag}
										</Text>
									)
								)}
							</Flex>
						) : (
							<Text size="sm" c="dimmed">
								No tags to display.
							</Text>
						)}
					</Box>
				</Flex>
			</Stack>
		</>
	);
};

export default ProfessionalLifePreview;
