import { Button, Paper, Title, Text, Stack } from "@mantine/core";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import { useAuth } from "../../contexts/AuthContext";

const FinalStep: React.FC = () => {
	const { fetchUser } = useAuth();

	const handleFinalSubmit = async () => {
		try {
			const response = await apiClient.post(
				"/api/lifeData/requestFinalReview"
			);
			notifications.show({
				title: "Success",
				message: response.data.message,
				color: "green",
			});
			fetchUser();
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Error",
					message:
						err?.response?.data?.message ||
						"Failed to submit for final review.",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to submit for final review.",
					color: "red",
				});
			}
		}
	};

	return (
		<Paper shadow="sm" p="xl" radius="md" withBorder>
			<Stack align="center" ta="center">
				<Title order={2}>You're Ready for the Final Step!</Title>
				<Text size="lg" mt="md">
					Thank you for filling out your profile details.
				</Text>
				<Text c="dimmed" mt="sm">
					To maintain the quality of our platform, the SM360 team will
					now conduct a final review of your profile. You will be
					notified via email as soon as your profile is approved and
					live.
				</Text>
				<Text c="dimmed" mt="xs">
					This process typically takes 24-48 hours.
				</Text>
				<Button onClick={handleFinalSubmit} mt="xl" size="md">
					Submit for Final Review
				</Button>
			</Stack>
		</Paper>
	);
};

export default FinalStep;
