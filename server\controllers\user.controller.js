export const changeAdminPanelView = async (req, res) => {
	try {
		const user = req.user;
		user.isAdminPanelUser = !user.isAdminPanelUser;
		await user.save();
		const message = user.isAdminPanelUser
			? "Signed in as admin successfully"
			: "Successfully exited the admin panel.";
		res.status(200).json({ message });
	} catch (error) {
		console.error("Error in signInAsAdmin:", error);
		res.status(500).json({ message: "Server error" });
	}
};
