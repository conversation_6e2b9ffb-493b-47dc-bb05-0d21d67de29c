export interface User {
	_id: string;
	email: string;
	role: number;
	token?: string;
	createdAt: string;
	onboardingStep: number;
	firstName: string;
	secondName: string;
	mobile: string;
	profileStatus: profileStatusDataType;
	title: string;
	image: string;
	address: string;
	birthday: string;
	city: string;
	gender: string;
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	workplaceName: string;
	workplaceAddress: string;
	birthCity: string;
	hometownCity: string;
	schools: { name: string; location: string }[];
	universities: { name: string; course: string; location: string }[];
	earlyLifeTags: string[];
	firstJob: { companyName: string; roles: string[] };
	subsequentJobs: { companyName: string; roles: string[] }[];
	professionalLifeTags: string[];
	currentLifeSummary: string;
	currentCities: string[];
	currentOrganizations: { name: string; role: string }[];
	frequentTravelCities: string[];
	currentLifeTags: string[];
	skills: string[];
	twitter: string;
	instagram: string;
	linkedIn: string;
	canApproveUser: string;
	onboardingStepCompleted: boolean;
	isAdminPanelUser: boolean;
}

export interface AuthContextType {
	user: User | null;
	loading: boolean;
	login: (email: string, password: string) => Promise<User | null>;
	logout: () => void;
	setUser: (user: User) => void;
	fetchUser: () => Promise<User | null>;
}

export interface UserCreation {
	_id: string;
	firstName: string;
	middleName?: string;
	secondName: string;
	email: string;
	mobile: string;
	role: number;
	createdAt?: string;
}

export interface UserProfile {
	firstName: string;
	middleName: string;
	secondName: string;
	currentOrganization: string;
	role: number;
	mobile: string;
	city: string;
	address: string;
	twitter: string;
	instagram: string;
	linkedIn: string;
	otherSocialHandles: string[];
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	image: string | File;
	email: string;
	// birthday: string;
	// gender: string;
	// workplaceAddress: string;
	// skills: string[];
}

export type videoDataType = "EarlyLife" | "ProfessionalLife" | "CurrentLife";

export type School = {
	name: string;
	location: string;
};

export type University = {
	name: string;
	course: string;
	location: string;
};

export type EarlyLifeDataType = {
	birthCity: string;
	hometownCity: string;
	schools: School[];
	universities: University[];
	earlyLifeTags: string[];
	videoUrl: string | null;
};

export type Job = {
	companyName: string;
	roles: string[];
};

export type ProfessionalLifeDataType = {
	firstJob: Job;
	subsequentJobs: Job[];
	professionalLifeTags: string[];
	videoUrl: string | null;
};

export type Organization = {
	name?: string;
	role?: string;
};

export type CurrentLifeDataType = {
	currentLifeSummary: string;
	currentCities: string[];
	frequentTravelCities: string[];
	currentOrganizations: Organization[];
	currentLifeTags: string[];
	videoUrl: string | null;
};

export type AllLifeDataType = {
	earlyLifeData: EarlyLifeDataType;
	professionalLifeData: ProfessionalLifeDataType;
	currentLifeData: CurrentLifeDataType;
};

export type BasicDetailsDataType = {
	firstName: string;
	secondName: string;
	email: string;
	role: number;
	mobile: string;
};

export type previewApprovedBasicDetailsDataType = {
	firstName: string;
	middleName: string;
	secondName: string;
	currentOrganization: string;
	role: number;
	mobile: string;
	city: string;
	address: string;
	twitter: string;
	instagram: string;
	linkedIn: string;
	otherSocialHandles: string[];
	introduction: string;
	quote: string;
	joy: string;
	contentLinks: string[];
	image: string;
	email: string;
};

export type previewApprovedResponseDataType = {
	basicDetails: previewApprovedBasicDetailsDataType;
	earlyLifeData: EarlyLifeDataType;
	professionalLifeData: ProfessionalLifeDataType;
	currentLifeData: CurrentLifeDataType;
	profileStatus: profileStatusDataType;
	isEarlyChange?: boolean;
	isProfessionalChange?: boolean;
	isCurrentChange?: boolean;
};

export type profileStatusDataType = "pending" | "approved" | "re-approved";

export type UserSearchResult = {
	_id: string;
	firstName: string;
	middleName: string;
	secondName: string;
	image: string;
	email: string;
	role: number;
};

export type UserRefererCrutorSearchResult = {
	_id: string;
	firstName: string;
	secondName: string;
	image: string;
	email: string;
	role: number;
	profileStatus: profileStatusDataType;
};
