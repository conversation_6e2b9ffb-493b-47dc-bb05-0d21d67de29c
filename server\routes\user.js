import express from "express";
import User from "../models/User.js";
import {
	ONBOARDING_STEP,
	rolesValues,
	rolesValuesNumToStr,
} from "../constants/index.js";
import {
	checkAdminPanelView,
	checkLogin,
	checkOnboardingStatus,
} from "../middleware/auth.js";
import { upload } from "../middleware/upload.js";
import {
	newUserWelcomeTemplate,
	profileStatusUpdateTemplate,
	userDeletionNotificationTemplate,
} from "../utils/emailTemplates.js";
import { APP_CONFIG } from "../config/env.js";
import { generateEncPassword, generatePassword } from "../utils/encryption.js";
import { sendEmail } from "../services/emaiService.js";
import { getPreviewVideoUrl } from "../services/awsSpace.js";
import { cleanDeletedEmail } from "../utils/index.js";
import { changeAdminPanelView } from "../controllers/user.controller.js";
const router = express.Router();

router.get("/", checkLogin, checkOnboardingStatus, async (req, res) => {
	try {
		const userId = req.user._id;
		const role = req.user.role;
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 10;
		const sortField = req.query.sort || "firstName";
		const searchQuery = req.query.search || "";
		const skip = (page - 1) * limit;

		let query = {
			_id: { $ne: userId },
			displayStatus: true,
		};

		if (role !== 1) {
			query.role = rolesValues.CommunityMember;
		}

		if (searchQuery.trim()) {
			const escapedSearchQuery = searchQuery.replace(
				/[.*+?^${}()|[\]\\]/g,
				"\\$&"
			);
			const searchRegex = new RegExp(escapedSearchQuery, "i");
			query.$or = [
				{ firstName: searchRegex },
				{ secondName: searchRegex },
				{ email: searchRegex },
			];
		}

		const [users, total] = await Promise.all([
			User.find(query)
				.collation({ locale: "en", strength: 2 })
				.sort({ [sortField]: 1 })
				.skip(skip)
				.limit(limit),
			User.countDocuments(query),
		]);

		if (searchQuery.trim() && total === 0) {
			return res.status(200).json({
				data: [],
				total: 0,
				page: 1,
				totalPages: 0,
			});
		}

		res.status(200).json({
			data: users,
			total,
			page,
			totalPages: Math.ceil(total / limit),
		});
	} catch (err) {
		console.error("Error in get-users:", err);
		res.status(500).json({ error: "Server Error" });
	}
});

router.post(
	"/create-user",
	checkLogin,
	checkOnboardingStatus,
	async (req, res) => {
		const { firstName, middleName, secondName, email, role, mobile } =
			req.body;
		if (!rolesValuesNumToStr[role]) {
			return res.status(400).json({ message: "Invalid role" });
		}

		const alreadyExists = await User.findOne({ email });
		if (alreadyExists) {
			return res.status(400).json({ message: "User already exists" });
		}

		const password = generatePassword(8);
		const hashedPassword = generateEncPassword(password);

		const user = new User({
			firstName,
			middleName,
			secondName,
			email,
			password: hashedPassword,
			role,
			mobile,
		});
		await user.save();

		console.log(`sending email to ${email}`);
		res.status(201).json(user);

		console.log(`email sent to ${email}`);
		await sendEmail({
			to: email,
			subject: "Welcome to 360 App!",
			html: newUserWelcomeTemplate(
				email,
				password,
				APP_CONFIG.FRONTEND_URL
			),
		});
	}
);

router.delete(
	"/delete/:id",
	checkLogin,
	checkOnboardingStatus,
	async (req, res) => {
		const { id } = req.params;
		try {
			const user = await User.findById(id);
			if (!user) {
				return res.status(404).json({ message: "User not found" });
			}
			if (user.displayStatus === false) {
				return res
					.status(400)
					.json({ message: "User already deleted" });
			}
			const email = user.email;
			const timestamp = Date.now();
			user.email = `${user.email}_deleted_${timestamp}`;

			user.displayStatus = false;

			await user.save();
			res.status(200).json({ message: "User deleted successfully" });
			await sendEmail({
				to: email,
				subject: "Your account has been deleted",
				html: userDeletionNotificationTemplate(
					`${user.firstName} ${user.middleName ?? ""} ${user.secondName}`
				),
			});
		} catch (error) {
			console.log(error);
			return res.status(500).json({ message: "Failed to deltet user" });
		}
	}
);

router.put(
	"/update-user/:id",
	checkLogin,
	checkOnboardingStatus,
	async (req, res) => {
		const { id } = req.params;
		const { firstName, middleName, secondName } = req.body;

		try {
			const updatedUser = await User.findByIdAndUpdate(
				id,
				{ firstName, middleName, secondName },
				{ new: true }
			);
			if (!updatedUser) {
				return res.status(404).json({ message: "User not found" });
			}
			res.status(200).json(updatedUser);
		} catch (error) {
			console.error("Error updating user:", error);
			res.status(500).json({ message: "Server Error" });
		}
	}
);

router.get("/user-profile", checkLogin, async (req, res) => {
	const userId = req.user._id;
	const userProfile = await User.findOne({ _id: userId });

	if (!userProfile) {
		return res.status(404).json({ message: "User not found" });
	}

	const dataToSend = {
		firstName: userProfile.firstName,
		middleName: userProfile.middleName,
		secondName: userProfile.secondName,
		// title: userProfile.title,
		image: userProfile.image,
		mobile: userProfile.mobile,
		email: userProfile.email,
		address: userProfile.address,
		// birthday: userProfile.birthday,
		city: userProfile.city,
		// gender: userProfile.gender,
		introduction: userProfile.introduction,
		quote: userProfile.quote,
		joy: userProfile.joy,
		contentLinks: userProfile.contentLinks,
		currentOrganization: userProfile.currentOrganization,
		// workplaceAddress: userProfile.workplaceAddress,
		// skills: userProfile.skills,
		twitter: userProfile.twitter,
		instagram: userProfile.instagram,
		linkedIn: userProfile.linkedIn,
		role: userProfile.role,
		otherSocialHandles: userProfile.otherSocialHandles,
	};
	res.status(200).json(dataToSend);
});

router.post(
	"/update-profile",
	checkLogin,
	upload.single("image"),
	async (req, res) => {
		try {
			const userId = req.user._id;
			const user = await User.findById(userId);

			if (!user) {
				throw new Error("User not found");
			}
			const updateData = { ...req.body };

			if (req.file) {
				updateData.image = `/uploads/image/${req.file.filename}`;
			}

			if (typeof updateData.skills === "string") {
				try {
					updateData.skills = JSON.parse(updateData.skills);
				} catch {
					updateData.skills = updateData.skills
						.split(",")
						.map(s => s.trim());
				}
			}
			if (typeof updateData.contentLinks === "string") {
				try {
					updateData.contentLinks = JSON.parse(
						updateData.contentLinks
					);
				} catch {
					updateData.contentLinks = updateData.contentLinks
						.split(",")
						.map(s => s.trim());
				}
			}
			if (typeof updateData.otherSocialHandles === "string") {
				try {
					updateData.otherSocialHandles = JSON.parse(
						updateData.otherSocialHandles
					);
				} catch {
					updateData.otherSocialHandles =
						updateData.otherSocialHandles
							.split(",")
							.map(s => s.trim());
				}
			}

			if (user.onboardingStep === ONBOARDING_STEP.BASIC_DETAILS) {
				updateData.onboardingStep = ONBOARDING_STEP.EARLY_LIFE_VIDEO;
			}
			const updatedUser = await User.findByIdAndUpdate(
				userId,
				{ $set: updateData },
				{ new: true, upsert: true }
			);

			res.status(200).json({
				message: "Profile updated successfully",
				user: updatedUser,
			});
		} catch (err) {
			console.log(err);
			res.status(500).json({
				message: err.message ?? "Unable to update user data",
			});
		}
	}
);

router.get("/review-user/:userId", checkLogin, async (req, res) => {
	const { userId } = req.params;
	const { view } = req.query;

	const viewMap = {
		searchView: [
			"basicDetails",
			"earlyLifeData",
			"professionalLifeData",
			"currentLifeData",
		],
		profileReviewView: [
			"basicDetails",
			"mobile",
			"earlyLifeData",
			"professionalLifeData",
			"currentLifeData",
		],
	};

	const isProfileReviewView = view === "profileReviewView";
	const selectedFields = viewMap[view] || viewMap.searchView;

	try {
		const user = await User.findById(userId);
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}
		const isAfterApproved =
			user.profileStatus === "approved" ||
			user.profileStatus === "re-approved";

		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (isProfileReviewView && user.profileStatus === "approved") {
			return res
				.status(400)
				.json({ message: "User profile is already approved" });
		}
		let earlyLifeData = user.earlyLifeData;
		let professionalLifeData = user.professionalLifeData;
		let currentLifeData = user.currentLifeData;

		let earlyLifeVideoPath = null;
		if (selectedFields.includes("earlyLifeData")) {
			if (earlyLifeData && earlyLifeData.videoId) {
				earlyLifeVideoPath = await getPreviewVideoUrl(
					userId,
					earlyLifeData.videoId
				);
			}
		}
		let professionalLifeVideoPath = null;
		if (selectedFields.includes("professionalLifeData")) {
			if (professionalLifeData && professionalLifeData.videoId) {
				professionalLifeVideoPath = await getPreviewVideoUrl(
					userId,
					professionalLifeData.videoId
				);
			}
		}
		let currentLifeVideoPath = null;
		if (selectedFields.includes("currentLifeData")) {
			if (currentLifeData && currentLifeData.videoId) {
				currentLifeVideoPath = await getPreviewVideoUrl(
					userId,
					currentLifeData.videoId
				);
			}
		}

		let dataToSend = {};

		if (selectedFields.includes("basicDetails")) {
			dataToSend.basicDetails = {
				firstName: user.firstName,
				middleName: user.middleName,
				secondName: user.secondName,
				email: user.email,
				role: user.role,
				image: user.image,
				// title: user.title,
				address: user.address,
				// birthday: user.birthday,
				city: user.city,
				// gender: user.gender,
				introduction: user.introduction,
				quote: user.quote,
				joy: user.joy,
				contentLinks: user.contentLinks,
				currentOrganization: user.currentOrganization,
				// workplaceName: user.workplaceName,
				// workplaceAddress: user.workplaceAddress,
				// skills: user.skills,
				twitter: user.twitter,
				instagram: user.instagram,
				linkedIn: user.linkedIn,
				otherSocialHandles: user.otherSocialHandles,
			};
			if (selectedFields.includes("mobile")) {
				dataToSend.basicDetails.mobile = user.mobile;
			}
		}

		if (isProfileReviewView && isAfterApproved) {
			if (user.updatedEarlyLifeData) {
				earlyLifeData = user.updatedEarlyLifeData;
				dataToSend.isEarlyChange = true;
			}
			if (user.updatedProfessionalLifeData) {
				professionalLifeData = user.updatedProfessionalLifeData;
				dataToSend.isProfessionalChange = true;
			}
			if (user.updatedCurrentLifeData) {
				currentLifeData = user.updatedCurrentLifeData;
				dataToSend.isCurrentChange = true;
			}
		}

		if (selectedFields.includes("earlyLifeData")) {
			dataToSend.earlyLifeData = earlyLifeData
				? {
						birthCity: earlyLifeData.birthCity,
						hometownCity: earlyLifeData.hometownCity,
						universities: earlyLifeData.universities,
						schools: earlyLifeData.schools,
						earlyLifeTags: earlyLifeData.earlyLifeTags,
						videoUrl: earlyLifeVideoPath,
					}
				: null;
		}

		if (selectedFields.includes("professionalLifeData")) {
			dataToSend.professionalLifeData = professionalLifeData
				? {
						firstJob: professionalLifeData.firstJob,
						subsequentJobs: professionalLifeData.subsequentJobs,
						professionalLifeTags:
							professionalLifeData.professionalLifeTags,
						videoUrl: professionalLifeVideoPath,
					}
				: null;
		}

		if (selectedFields.includes("currentLifeData")) {
			dataToSend.currentLifeData = currentLifeData
				? {
						currentLifeSummary: currentLifeData.currentLifeSummary,
						currentCities: currentLifeData.currentCities,
						currentOrganizations:
							currentLifeData.currentOrganizations,
						frequentTravelCities:
							currentLifeData.frequentTravelCities,
						currentLifeTags: currentLifeData.currentLifeTags,
						videoUrl: currentLifeVideoPath,
					}
				: null;
		}

		dataToSend.profileStatus = user.profileStatus;
		return res.status(200).json(dataToSend);
	} catch (error) {
		console.error("Error retrieving user:", error);
		res.status(500).json({ message: "Server Error" });
	}
});

router.get("/pending-approvals", checkLogin, async (req, res) => {
	try {
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 10;
		const sortField = req.query.sort || "firstName";
		const skip = (page - 1) * limit;
		const searchQuery = req.query.search || "";

		const allowedProfileStatuses = ["all", "pending", "re-approved"];
		let profileStatus = req.query.profileStatus;

		if (!allowedProfileStatuses.includes(profileStatus)) {
			profileStatus = "all";
		}

		const query = {
			displayStatus: true,
		};

		if (profileStatus === "pending") {
			query.onboardingStep = ONBOARDING_STEP.WAIT_FOR_APPROVAL;
		} else if (profileStatus === "re-approved") {
			query.profileStatus = "re-approved";
		} else if (profileStatus === "all") {
			query.$or = [
				{ onboardingStep: ONBOARDING_STEP.WAIT_FOR_APPROVAL },
				{ profileStatus: "re-approved" },
			];
		}

		if (searchQuery.trim()) {
			const escapedSearchQuery = searchQuery.replace(
				/[.*+?^${}()|[\]\\]/g,
				"\\$&"
			);
			const searchRegex = new RegExp(escapedSearchQuery, "i");

			// If query already has $or from "all", merge it with search $or
			if (query.$or) {
				query.$and = [
					{ $or: query.$or },
					{
						$or: [
							{ firstName: searchRegex },
							{ secondName: searchRegex },
							{ email: searchRegex },
						],
					},
				];
				delete query.$or;
			} else {
				query.$or = [
					{ firstName: searchRegex },
					{ secondName: searchRegex },
					{ email: searchRegex },
				];
			}
		}

		const [users, total] = await Promise.all([
			User.find(query)
				.collation({ locale: "en", strength: 2 })
				.sort({ [sortField]: 1 })
				.skip(skip)
				.limit(limit),
			User.countDocuments(query),
		]);

		if (searchQuery.trim() && total === 0) {
			return res.status(200).json({
				data: [],
				total: 0,
				page: 1,
				totalPages: 0,
			});
		}

		res.status(200).json({
			data: users,
			total,
			page,
			totalPages: Math.ceil(total / limit),
		});
	} catch (err) {
		console.error("Error in get-pending-approvals:", err);
		res.status(500).json({ error: "Server Error" });
	}
});

router.put("/basic-details/:userId", checkLogin, async (req, res) => {
	const { userId } = req.params;
	const { firstName, lastName, email, role, mobile } = req.body;

	const user = await User.findById(userId);
	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}
	if (user.displayStatus === false) {
		return res.status(404).json({ message: "User not found" });
	}
	const updatedUser = await User.findByIdAndUpdate(
		userId,
		{
			firstName,
			secondName: lastName,
			email,
			role,
			mobile,
		},
		{
			new: true,
		}
	);

	if (!updatedUser) {
		return res.status(404).json({ message: "User not found" });
	}
	res.status(200).json({
		message: "User updated successfully",
	});
});

function mergeLifeData(original, updated) {
	if (!updated) return original;
	return {
		...(original.toObject?.() || original),
		...(updated.toObject?.() || updated),
		videoId: original?.videoId || updated?.videoId || null,
	};
}

router.put("/change-profile-status/:userId", checkLogin, async (req, res) => {
	const { userId } = req.params;
	const { status, refererId, curatorId } = req.body;
	if (!status) {
		return res.status(400).json({ message: "Status is required" });
	}

	const user = await User.findById(userId);
	if (!user) {
		return res.status(404).json({ message: "User not found" });
	}
	if (user.displayStatus === false) {
		return res.status(404).json({ message: "User not found" });
	}

	let newStatus = status;
	if (status === "approved" || status === "re-approved") {
		newStatus = "approved";
	}
	try {
		const updatePayload = {
			profileStatus: newStatus,
		};
		const unsetPayload = {};

		if (status === "approved") {
			if (!refererId || !curatorId) {
				return res.status(400).json({
					message: "Referer and Curator are required for approval",
				});
			}
			updatePayload.onboardingStep = ONBOARDING_STEP.COMPLETED;
			updatePayload.referer = refererId;
			updatePayload.curator = curatorId;
		} else if (status === "re-approved") {
			if (user.updatedEarlyLifeData) {
				updatePayload.earlyLifeData = mergeLifeData(
					user.earlyLifeData,
					user.updatedEarlyLifeData
				);
				unsetPayload.updatedEarlyLifeData = 1;
			}
			if (user.updatedProfessionalLifeData) {
				updatePayload.professionalLifeData = mergeLifeData(
					user.professionalLifeData,
					user.updatedProfessionalLifeData
				);
				unsetPayload.updatedProfessionalLifeData = 1;
			}
			if (user.updatedCurrentLifeData) {
				updatePayload.currentLifeData = mergeLifeData(
					user.currentLifeData,
					user.updatedCurrentLifeData
				);
				unsetPayload.updatedCurrentLifeData = 1;
			}
		}

		const updatedUser = await User.findByIdAndUpdate(
			userId,
			{
				$set: updatePayload,
				...(Object.keys(unsetPayload).length
					? { $unset: unsetPayload }
					: {}),
			},
			{ new: true }
		);
		if (!updatedUser) {
			return res.status(404).json({ message: "User not found" });
		}

		const userEmail = updatedUser.email;
		const userName = `${updatedUser.firstName} ${updatedUser.secondName}`;
		const frontendBaseUrl = APP_CONFIG.FRONTEND_URL;
		const profileLink = `${frontendBaseUrl}/profile`;

		res.status(200).json({ message: `User ${status} successfully` });
		await sendEmail({
			to: userEmail,
			subject: `Your Profile Status is now ${status}`,
			html: profileStatusUpdateTemplate(userName, status, profileLink),
		});
	} catch (error) {
		console.error(`Error in ${status} user:`, error);
		res.status(500).json({ message: "Server Error" });
	}
});

router.get("/search-referer-curator", checkLogin, async (req, res) => {
	try {
		const { query } = req.query;
		if (!query) {
			return res
				.status(400)
				.json({ message: "Search query is required" });
		}

		const escapedQuery = query
			.trim()
			.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

		const users = await User.find({
			$and: [
				{ profileStatus: { $in: ["approved", "re-approved"] } },
				{ displayStatus: true },
				{
					$or: [
						{ firstName: { $regex: escapedQuery, $options: "i" } },
						{ secondName: { $regex: escapedQuery, $options: "i" } },
						{ email: { $regex: escapedQuery, $options: "i" } },
					],
				},
			],
		}).select("firstName secondName image email role profileStatus");

		res.status(200).json(users);
	} catch (error) {
		console.error("Error searching users:", error);
		res.status(500).json({ message: "Server Error" });
	}
});

router.get("/search", checkLogin, async (req, res) => {
	try {
		const { query } = req.query;
		if (!query) {
			return res
				.status(400)
				.json({ message: "Search query is required" });
		}

		const escapedQuery = query
			.trim()
			.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

		const users = await User.find({
			$and: [
				{ _id: { $ne: req.user._id } },
				{ profileStatus: { $in: ["approved", "re-approved"] } },
				{ displayStatus: true },
				{
					$or: [
						{ firstName: { $regex: escapedQuery, $options: "i" } },
						{ secondName: { $regex: escapedQuery, $options: "i" } },
						{ middleName: { $regex: escapedQuery, $options: "i" } },
						{ email: { $regex: escapedQuery, $options: "i" } },

						{
							"earlyLifeData.earlyLifeTags": {
								$regex: escapedQuery,
								$options: "i",
							},
						},
						// Search in professionalLifeData tags
						{
							"professionalLifeData.professionalLifeTags": {
								$regex: escapedQuery,
								$options: "i",
							},
						},
						// Search in currentLifeData tags
						{
							"currentLifeData.currentLifeTags": {
								$regex: escapedQuery,
								$options: "i",
							},
						},
					],
				},
			],
		}).select(
			"firstName middleName secondName image email role profileStatus"
		);

		res.status(200).json(users);
	} catch (error) {
		console.error("Error searching users:", error);
		res.status(500).json({ message: "Server Error" });
	}
});

router.post(
	"/update-profile/:userId",
	checkLogin,
	upload.single("image"),
	async (req, res) => {
		try {
			const { userId } = req.params;
			const user = await User.findById(userId);

			if (!user) {
				return res.status(404).json({ message: "User not found" });
			}
			if (user.displayStatus === false) {
				return res.status(404).json({ message: "User not found" });
			}
			const updateData = {
				firstName: req.body.firstName?.trim() || "",
				secondName: req.body.secondName?.trim() || "",
				middleName: req.body.middleName?.trim() || "",
				address: req.body.address?.trim() || "",
				city: req.body.city?.trim() || "",
				introduction: req.body.introduction?.trim() || "",
				quote: req.body.quote?.trim() || "",
				joy: req.body.joy?.trim() || "",
				currentOrganization: req.body.currentOrganization?.trim() || "",
				twitter: req.body.twitter?.trim() || "",
				instagram: req.body.instagram?.trim() || "",
				linkedIn: req.body.linkedIn?.trim() || "",
			};

			if (req.file) {
				updateData.image = `/uploads/video/${req.file.filename}`;
			}

			["contentLinks", "otherSocialHandles"].forEach(key => {
				const value = req.body[key];
				if (typeof value === "string") {
					try {
						updateData[key] = JSON.parse(value);
					} catch {
						updateData[key] = value
							.split(",")
							.map(s => s.trim())
							.filter(s => s !== "");
					}
				} else if (Array.isArray(value)) {
					updateData[key] = value.filter(s => s.trim() !== "");
				} else {
					updateData[key] = [];
				}
			});

			const updatedUser = await User.findByIdAndUpdate(
				userId,
				{ $set: updateData },
				{ new: true }
			);

			res.status(200).json({
				message: "Profile updated successfully",
				user: updatedUser,
			});
		} catch (err) {
			console.error(err);
			res.status(500).json({
				message: err.message ?? "Unable to update user data",
			});
		}
	}
);

router.get("/referer-curator-user/:userId", checkLogin, async (req, res) => {
	try {
		const { userId } = req.params;
		const user = await User.findById(userId);

		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}
		const refererId = user.referer;
		const curatorId = user.curator;
		const refererUser = await User.findById(refererId);
		const curatorUser = await User.findById(curatorId);

		res.status(200).json({
			referer: {
				email: refererUser.displayStatus
					? refererUser.email
					: cleanDeletedEmail(refererUser.email),
				_id: refererUser._id,
			},
			curator: {
				email: curatorUser.displayStatus
					? curatorUser.email
					: cleanDeletedEmail(curatorUser.email),
				_id: curatorUser._id,
			},
		});
	} catch (err) {
		console.error("Error in get referer carutor user:", err);
		res.status(500).json({ error: "Server Error" });
	}
});

router.post(
	"/changeAdminPanelView",
	checkLogin,
	checkAdminPanelView,
	changeAdminPanelView
);

export default router;
